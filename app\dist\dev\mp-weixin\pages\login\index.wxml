<layout-default-uni class="data-v-45258083" u-s="{{['d']}}" u-i="45258083-0" bind:__l="__l"><view class="login-container data-v-45258083"><view class="logo-section data-v-45258083"><image class="logo data-v-45258083" src="{{a}}" mode="aspectFit"/><text class="app-name data-v-45258083"> 点价系统 </text></view><view class="login-form data-v-45258083"><view class="login-tabs data-v-45258083"><view class="{{['tab-item', 'data-v-45258083', b && 'active']}}" bindtap="{{c}}"> 账号密码登录 </view><view class="{{['tab-item', 'data-v-45258083', d && 'active']}}" bindtap="{{e}}"> 手机号登录 </view></view><view wx:if="{{f}}" class="form-content data-v-45258083"><view class="form-item data-v-45258083"><text class="form-label data-v-45258083"> 手机号 </text><view class="form-control data-v-45258083"><input type="number" maxlength="{{11}}" placeholder="请输入手机号" placeholder-class="placeholder" class="form-input data-v-45258083" value="{{g}}" bindinput="{{h}}"></input></view></view><view class="form-item data-v-45258083"><text class="form-label data-v-45258083"> 验证码 </text><view class="form-control data-v-45258083"><input type="number" maxlength="{{6}}" placeholder="6位数字" placeholder-class="placeholder" class="form-input data-v-45258083" value="{{i}}" bindinput="{{j}}"></input><wd-button wx:if="{{m}}" u-s="{{['d']}}" class="code-btn data-v-45258083" bindclick="{{l}}" u-i="45258083-1,45258083-0" bind:__l="__l" u-p="{{m}}">{{k}}</wd-button></view></view><wd-button wx:if="{{o}}" u-s="{{['d']}}" class="login-btn data-v-45258083" bindclick="{{n}}" u-i="45258083-2,45258083-0" bind:__l="__l" u-p="{{o}}"> 登录 </wd-button></view><view wx:if="{{p}}" class="form-content data-v-45258083"><view class="form-item data-v-45258083"><text class="form-label data-v-45258083"> 用户名 </text><view class="form-control data-v-45258083"><input type="text" placeholder="请输入用户名" placeholder-class="placeholder" class="form-input data-v-45258083" value="{{q}}" bindinput="{{r}}"></input></view></view><view class="form-item data-v-45258083"><text class="form-label data-v-45258083"> 密码 </text><view class="form-control data-v-45258083"><input type="password" placeholder="请输入密码" placeholder-class="placeholder" class="form-input data-v-45258083" value="{{s}}" bindinput="{{t}}"></input></view></view><view wx:if="{{v}}" class="form-item data-v-45258083"><text class="form-label data-v-45258083"> 验证码 </text><view class="form-control data-v-45258083"><input type="text" maxlength="{{w}}" placeholder="请输入验证码" placeholder-class="placeholder" class="form-input captcha-input data-v-45258083" value="{{x}}" bindinput="{{y}}"></input><view class="captcha-image data-v-45258083" bindtap="{{B}}"><image wx:if="{{z}}" src="{{A}}" mode="aspectFit" class="captcha-img data-v-45258083"/><text wx:else class="captcha-loading data-v-45258083">获取中...</text></view></view></view><wd-button wx:if="{{D}}" u-s="{{['d']}}" class="login-btn data-v-45258083" bindclick="{{C}}" u-i="45258083-3,45258083-0" bind:__l="__l" u-p="{{D}}"> 登录 </wd-button></view><view class="divider data-v-45258083"><text class="data-v-45258083">或</text></view><wd-button wx:if="{{F}}" u-s="{{['d']}}" class="wx-login-btn data-v-45258083" bindclick="{{E}}" u-i="45258083-4,45258083-0" bind:__l="__l" u-p="{{F}}"><text class="i-carbon-logo-wechat data-v-45258083"/> 微信登录 </wd-button></view><view class="agreement data-v-45258083"><text class="agreement-text data-v-45258083"> 登录即表示同意 <text class="link data-v-45258083" bindtap="{{G}}"> 《用户协议》 </text> 和 <text class="link data-v-45258083" bindtap="{{H}}"> 《隐私政策》 </text></text></view></view></layout-default-uni>